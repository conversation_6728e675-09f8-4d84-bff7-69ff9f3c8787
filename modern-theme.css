/* Modern Theme for Vector-Fire
   A professional, contemporary design for military applications */

:root {
  /* Modern color palette */
  --modern-bg: #eef2f7;
  --modern-panel-bg: #ffffff;
  --modern-bg-pattern: rgba(0, 0, 0, 0.02);
  --modern-text: #2c3e50;
  --modern-text-light: #7f8c8d;
  --modern-primary: #3498db;
  --modern-primary-dark: #2980b9;
  --modern-secondary: #2ecc71;
  --modern-secondary-dark: #27ae60;
  --modern-danger: #e74c3c;
  --modern-danger-dark: #c0392b;
  --modern-warning: #f39c12;
  --modern-friendly: #3498db;
  --modern-enemy: #e74c3c;
  --modern-border: #e0e6ed;
  --modern-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  --modern-shadow-hover: 0 5px 15px rgba(0, 0, 0, 0.1);

  /* Typography */
  --modern-font-main: 'Roboto', 'Segoe UI', sans-serif;
  --modern-font-heading: 'Roboto Condensed', 'Arial', sans-serif;
  --modern-font-mono: 'Roboto Mono', monospace;
}

/* Base styles */
body:not(.retro-mode) {
  background-color: var(--modern-bg);
  background-image:
    linear-gradient(var(--modern-bg-pattern) 1px, transparent 1px),
    linear-gradient(90deg, var(--modern-bg-pattern) 1px, transparent 1px);
  background-size: 20px 20px;
  color: var(--modern-text);
  font-family: var(--modern-font-main);
  line-height: 1.6;
  position: relative;
}

/* Typography */
body:not(.retro-mode) h1,
body:not(.retro-mode) h2,
body:not(.retro-mode) h3,
body:not(.retro-mode) h4 {
  font-family: var(--modern-font-heading);
  font-weight: 500;
  color: var(--modern-text);
  margin-bottom: 16px;
}

body:not(.retro-mode) h1 {
  font-size: 2.2rem;
  letter-spacing: -0.5px;
  border-bottom: 2px solid var(--modern-primary);
  padding-bottom: 8px;
  display: inline-block;
}

body:not(.retro-mode) h2 {
  font-size: 1.5rem;
  letter-spacing: -0.3px;
}

body:not(.retro-mode) h3 {
  font-size: 1.2rem;
  letter-spacing: -0.2px;
}

body:not(.retro-mode) p {
  margin-bottom: 16px;
}

/* Container */
body:not(.retro-mode) .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(to bottom, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0.9) 100%);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Panels */
body:not(.retro-mode) .control-panel,
body:not(.retro-mode) #controls-container,
body:not(.retro-mode) #mortar-controls-container {
  background-color: var(--modern-panel-bg);
  border-radius: 8px;
  box-shadow: var(--modern-shadow);
  border: 1px solid var(--modern-border);
  transition: box-shadow 0.3s ease;
}

body:not(.retro-mode) .control-panel:hover {
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) section {
  padding: 20px;
  margin-bottom: 20px;
}

/* Collapsible sections */
body:not(.retro-mode) .collapsible {
  background-color: var(--modern-panel-bg);
  color: var(--modern-text);
  cursor: pointer;
  padding: 16px 20px;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: var(--modern-shadow);
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid var(--modern-border);
  margin-bottom: 16px;
}

body:not(.retro-mode) .collapsible:hover {
  background-color: #f8f9fa;
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) .collapsible:after {
  content: '\002B';
  color: var(--modern-primary);
  font-weight: bold;
  float: right;
  margin-left: 5px;
  transition: transform 0.3s ease;
}

body:not(.retro-mode) .active:after {
  content: "\2212";
  color: var(--modern-primary-dark);
}

body:not(.retro-mode) .content {
  padding: 0 20px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: var(--modern-panel-bg);
  border-radius: 0 0 8px 8px;
  border: 1px solid var(--modern-border);
  border-top: none;
  margin-top: -16px;
  margin-bottom: 20px;
}

body:not(.retro-mode) .active + .content {
  padding: 20px;
  border-top: none;
}

/* Form elements */
body:not(.retro-mode) input,
body:not(.retro-mode) select,
body:not(.retro-mode) textarea {
  width: 100%;
  padding: 10px 12px;
  margin-bottom: 16px;
  border: 1px solid var(--modern-border);
  border-radius: 4px;
  background-color: #fff;
  color: var(--modern-text);
  font-family: var(--modern-font-main);
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

body:not(.retro-mode) input:focus,
body:not(.retro-mode) select:focus,
body:not(.retro-mode) textarea:focus {
  outline: none;
  border-color: var(--modern-primary);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

body:not(.retro-mode) label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--modern-text);
}

/* Buttons */
body:not(.retro-mode) button {
  background-color: var(--modern-primary);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-family: var(--modern-font-main);
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

body:not(.retro-mode) button:hover:not(:disabled) {
  background-color: var(--modern-primary-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

body:not(.retro-mode) button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

body:not(.retro-mode) button:disabled {
  background-color: #cbd5e0;
  color: #718096;
  cursor: not-allowed;
  box-shadow: none;
}

/* Action buttons */
body:not(.retro-mode) #add-target-button,
body:not(.retro-mode) #calculate-solution-button {
  background-color: var(--modern-secondary);
}

body:not(.retro-mode) #add-target-button:hover,
body:not(.retro-mode) #calculate-solution-button:hover {
  background-color: var(--modern-secondary-dark);
}

body:not(.retro-mode) #clear-targets-button,
body:not(.retro-mode) #clear-targets-button-mortar,
body:not(.retro-mode) .remove-button {
  background-color: var(--modern-danger);
}

body:not(.retro-mode) #clear-targets-button:hover,
body:not(.retro-mode) #clear-targets-button-mortar:hover,
body:not(.retro-mode) .remove-button:hover {
  background-color: var(--modern-danger-dark);
}

/* Button groups */
body:not(.retro-mode) .button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

/* Lists */
body:not(.retro-mode) #targets-list,
body:not(.retro-mode) #received-targets-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--modern-border);
  border-radius: 4px;
}

body:not(.retro-mode) #targets-list li,
body:not(.retro-mode) #received-targets-list li {
  padding: 12px 16px;
  border-bottom: 1px solid var(--modern-border);
  background-color: #fff;
  transition: all 0.2s ease;
  cursor: pointer;
}

body:not(.retro-mode) #targets-list li:last-child,
body:not(.retro-mode) #received-targets-list li:last-child {
  border-bottom: none;
}

body:not(.retro-mode) #targets-list li:hover,
body:not(.retro-mode) #received-targets-list li:hover {
  background-color: #f8fafc;
}

body:not(.retro-mode) #targets-list li.selected,
body:not(.retro-mode) #received-targets-list li.selected {
  background-color: rgba(52, 152, 219, 0.1);
  border-left: 3px solid var(--modern-primary);
}

/* Map container */
body:not(.retro-mode) #map-container,
body:not(.retro-mode) #mortar-map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--modern-shadow);
  border: 1px solid var(--modern-border);
  transition: box-shadow 0.3s ease;
}

body:not(.retro-mode) #map-container:hover,
body:not(.retro-mode) #mortar-map-container:hover {
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) #map,
body:not(.retro-mode) #mortar-map {
  border-radius: 8px;
}

/* Map legend */
body:not(.retro-mode) #map-legend {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: var(--modern-shadow);
  border: 1px solid var(--modern-border);
  padding: 12px;
  transition: all 0.3s ease;
}

body:not(.retro-mode) #map-legend h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1rem;
  color: var(--modern-text);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

body:not(.retro-mode) #map-legend.collapsed {
  background-color: rgba(255, 255, 255, 0.8);
}

body:not(.retro-mode) #map-legend.collapsed:hover {
  background-color: rgba(255, 255, 255, 0.95);
}

/* Force type indicators */
body:not(.retro-mode) .legend-friendly {
  background-color: var(--modern-friendly);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

body:not(.retro-mode) .legend-enemy {
  background-color: var(--modern-enemy);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Target markers */
body:not(.retro-mode) .target-marker.target-friendly div {
  background-color: var(--modern-friendly) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

body:not(.retro-mode) .target-marker.target-enemy div {
  background-color: var(--modern-enemy) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

/* Glow animations */
@keyframes modern-friendly-glow {
  0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(52, 152, 219, 0.4); }
  100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
}

@keyframes modern-enemy-glow {
  0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(231, 76, 60, 0.4); }
  100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
}

body:not(.retro-mode) .target-marker.target-friendly div {
  animation: modern-friendly-glow 2s infinite;
}

body:not(.retro-mode) .target-marker.target-enemy div {
  animation: modern-enemy-glow 2s infinite;
}

/* Scrollbar styling */
body:not(.retro-mode)::-webkit-scrollbar {
  width: 10px;
}

body:not(.retro-mode)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

body:not(.retro-mode)::-webkit-scrollbar-thumb {
  background: #c5d0db;
  border-radius: 10px;
}

body:not(.retro-mode)::-webkit-scrollbar-thumb:hover {
  background: #a9b9c9;
}

/* Solution display */
body:not(.retro-mode) #solution-display {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
  border: 1px solid var(--modern-border);
  transition: all 0.3s ease;
}

body:not(.retro-mode) #solution-display.calculated {
  background-color: rgba(46, 204, 113, 0.1);
  border-color: var(--modern-secondary);
}

body:not(.retro-mode) .calculation-confirmation {
  color: var(--modern-secondary-dark);
  font-weight: 600;
  text-align: center;
  margin-top: 12px;
  padding: 8px;
  background-color: rgba(46, 204, 113, 0.1);
  border-radius: 4px;
}

/* Theme toggle button */
body:not(.retro-mode) .theme-toggle-button {
  background-color: #34495e;
  color: white;
  border: none;
  font-weight: 500;
  letter-spacing: 0.5px;
}

body:not(.retro-mode) .theme-toggle-button:hover {
  background-color: #2c3e50;
}

/* Leaflet controls */
body:not(.retro-mode) .leaflet-control-zoom a {
  background-color: white;
  color: var(--modern-text);
  border-color: var(--modern-border);
  box-shadow: var(--modern-shadow);
  transition: all 0.2s ease;
}

body:not(.retro-mode) .leaflet-control-zoom a:hover {
  background-color: #f8fafc;
  color: var(--modern-primary);
}

/* Loading overlay */
body:not(.retro-mode) .loading-overlay {
  background-color: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(3px);
  backdrop-filter: blur(3px);
  color: var(--modern-text);
  font-weight: 500;
}

/* Screen Navigation - Modern Style */
body:not(.retro-mode) .screen-navigation {
  background: linear-gradient(135deg, var(--modern-primary), var(--modern-secondary));
  border: none;
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) .screen-navigation h1 {
  color: white;
  font-family: var(--modern-font-heading);
  font-weight: 500;
  margin: 0;
}

body:not(.retro-mode) .nav-button {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-family: var(--modern-font-main);
  font-weight: 500;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

body:not(.retro-mode) .nav-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: var(--modern-shadow);
}

body:not(.retro-mode) .nav-button.active {
  background: rgba(255, 255, 255, 0.95);
  color: var(--modern-primary);
  border-color: white;
  font-weight: 600;
  box-shadow: var(--modern-shadow-hover);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  body:not(.retro-mode) h1 {
    font-size: 1.8rem;
  }

  body:not(.retro-mode) h2 {
    font-size: 1.3rem;
  }

  body:not(.retro-mode) section {
    padding: 15px;
  }

  body:not(.retro-mode) .button-group {
    flex-direction: column;
    gap: 8px;
  }
}

/* Add Google Fonts for the modern theme */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Condensed:wght@400;700&family=Roboto+Mono&display=swap');
