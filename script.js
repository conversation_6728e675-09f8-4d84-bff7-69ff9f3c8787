// --- Constants and Global Variables ---
// Centered roughly on Augusta/Martinez area, or use null for auto-location attempt
const initialCoords = [33.4735, -82.0740];
const map = L.map('map').setView(initialCoords, 13);
const targetList = document.querySelector('#target-list-section #target-list-items');
const sendFeedbackEl = document.getElementById('send-feedback'); // Feedback element
const drawGridButton = document.getElementById('draw-grid-button'); // Get the custom draw grid button
const clearGridLinesButton = document.getElementById('clear-grid-lines-button'); // Get the clear grid lines button
let highlightedCell = null;
let targetMarkers = []; // Holds the L.marker objects
let currentGridLines = [];
let drawnRectangle = null;
const LOCAL_STORAGE_KEY = 'fscsObserverTargets'; // Key for local storage

// --- Initialization ---
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: 'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors',
    maxZoom: 19, // Set max zoom level supported by tiles
}).addTo(map);

// --- Local Storage Functions ---
function saveTargetsToLocalStorage() {
    const targetsToSave = [];
    targetList.querySelectorAll('li').forEach(item => {
        if (item.targetData) { // Check if targetData exists
            targetsToSave.push(item.targetData);
        }
    });
    try {
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(targetsToSave));
        console.log("Targets saved to localStorage");
    } catch (e) {
        console.error("Error saving to localStorage:", e);
        alert("Could not save targets. Local storage might be full or disabled.");
    }
}

function loadTargetsFromLocalStorage() {
    const savedTargets = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedTargets) {
        try {
            const parsedTargets = JSON.parse(savedTargets);
            // Clear existing list/markers before loading to prevent duplicates
            clearAllTargets(false); // Pass false to skip confirmation and storage clearing
            parsedTargets.forEach(targetData => {
                // Add target back to list and map without saving again immediately
                addSavedTargetToListAndMap(targetData);
            });
            console.log(`Loaded ${parsedTargets.length} targets from localStorage`);
        } catch (e) {
            console.error("Error parsing targets from localStorage:", e);
            localStorage.removeItem(LOCAL_STORAGE_KEY); // Clear invalid data
        }
    }
}

// --- Target Management Functions ---

// Helper to add a target item and marker (used by addTarget and load)
function addTargetToListAndMap(targetData) {
    const { forceType, targetType, quantity, locationText, gridInfo, latlng } = targetData; // Use latlng which should be L.latLng object here
    let markerColor = forceType === 'enemy' ? 'red' : 'blue';
    let markerShapeHTML = '';
    const markerSize = 16; // Reduced from 20px to 16px

    // Determine marker HTML based on type with distinctive shapes
    // Use solid color based on force type (no white background for enemy)
    const bgColor = markerColor; // Always use the marker color (red or blue)
    const borderStyle = `1px solid ${forceType === 'enemy' ? '#990000' : '#000066'}`; // Darker border for definition

    // Common size for all icons
    const iconWidth = markerSize;
    const iconHeight = markerSize;

    switch (targetType) {
        case 'infantry':
            // Circle
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px; border-radius: 50%;"></div>
            `;
            break;
        case 'tank':
            // Triangle
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <div style="width: 0; height: 0; border-left: ${iconWidth/2}px solid transparent; border-right: ${iconWidth/2}px solid transparent; border-bottom: ${iconHeight}px solid ${bgColor}; position: relative; border-top: 0; outline: ${borderStyle}; outline-offset: -1px;"></div>
                </div>
            `;
            break;
        case 'mg':
            // Square
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px;"></div>
            `;
            break;
        case 'artillery':
            // Diamond
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <div style="position: absolute; width: ${iconWidth}px; height: ${iconHeight}px; background-color: ${bgColor}; transform: rotate(45deg); border: ${borderStyle};"></div>
                </div>
            `;
            break;
        case 'antiair':
            // Diamond with cross
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <div style="position: absolute; width: ${iconWidth}px; height: ${iconHeight}px; background-color: ${bgColor}; transform: rotate(45deg); border: ${borderStyle};"></div>
                    <div style="position: absolute; top: ${iconHeight/2-0.5}px; left: 0; width: 100%; height: 1px; background-color: ${markerColor};"></div>
                    <div style="position: absolute; top: 0; left: ${iconWidth/2-0.5}px; width: 1px; height: 100%; background-color: ${markerColor};"></div>
                </div>
            `;
            break;
        case 'recon':
            // Hexagon
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth*0.25},0 ${iconWidth*0.75},0 ${iconWidth},${iconHeight*0.5} ${iconWidth*0.75},${iconHeight} ${iconWidth*0.25},${iconHeight} 0,${iconHeight*0.5}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `;
            break;
        case 'apc':
            // Rectangle with rounded corners
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth*1.2}px; height: ${iconHeight*0.8}px; border-radius: 5px;"></div>
            `;
            break;
        case 'heli':
            // Pentagon
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth/2},0 ${iconWidth},${iconHeight/3} ${iconWidth*0.8},${iconHeight} ${iconWidth*0.2},${iconHeight} 0,${iconHeight/3}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `;
            break;
        case 'command':
            // Star
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth/2},0 ${iconWidth*0.65},${iconHeight*0.35} ${iconWidth},${iconHeight*0.35} ${iconWidth*0.75},${iconHeight*0.6} ${iconWidth*0.85},${iconHeight} ${iconWidth/2},${iconHeight*0.75} ${iconWidth*0.15},${iconHeight} ${iconWidth*0.25},${iconHeight*0.6} 0,${iconHeight*0.35} ${iconWidth*0.35},${iconHeight*0.35}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `;
            break;
        default: // Fallback for unknown types
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px; border-radius: 50%;"></div>
            `;
            break;
    }

    const listItem = document.createElement('li');
    listItem.classList.add('new-target'); // Add class for animation
    // Store data on the list item itself for easier access later
    // Ensure we store the plain latlng object for saving, but keep L.latLng for use
    listItem.targetData = {
         ...targetData, // Copy existing data
         latlng: { lat: latlng.lat, lng: latlng.lng } // Store plain object for saving
    };

    listItem.innerHTML = `
        <div><strong>Force:</strong> ${forceType === 'enemy' ? 'Enemy' : 'Friendly'}</div>
        <div><strong>Type:</strong> ${targetType}</div>
        <div><strong>Quantity:</strong> ${quantity}</div>
        <div><strong>Location:</strong> ${locationText}${gridInfo}</div>
        <div class="button-group">
            <button class="go-to-button" title="Zoom to target">Go To</button>
            <button class="remove-button" title="Remove target">Remove</button>
        </div>
    `;
    targetList.appendChild(listItem);

    // Create the marker using the L.latLng object passed in targetData
    const marker = L.marker(latlng, {
        title: `${forceType} ${targetType} (${quantity})`, // Add title for hover tooltip
        icon: L.divIcon({
            className: `target-marker target-${forceType} target-${targetType}`,
            iconSize: [markerSize, markerSize],
            // Adjust anchor based on shape (bottom-center for tank, center-center for others)
            iconAnchor: targetType === 'tank' ? [markerSize / 2, markerSize] : [markerSize / 2, markerSize / 2],
            html: markerShapeHTML
        })
    }).addTo(map);
    targetMarkers.push(marker); // Add marker to the global array

    listItem.marker = marker; // Link marker to list item

    // Add event listeners for buttons
    listItem.querySelector('.go-to-button').addEventListener('click', () => {
        console.log('Go To clicked for:', latlng); // Use the L.latLng object directly
        if (latlng) {
            map.setView(latlng, 19); // Zoom level set to 19
        }
    });

    listItem.querySelector('.remove-button').addEventListener('click', () => {
        map.removeLayer(listItem.marker);
        const markerIndex = targetMarkers.indexOf(listItem.marker);
        if (markerIndex > -1) {
            targetMarkers.splice(markerIndex, 1);
        }
        listItem.remove();
        saveTargetsToLocalStorage(); // Save after removing
    });

     // Remove the 'new-target' class after a short delay to allow the animation to play
     setTimeout(() => {
        listItem.classList.remove('new-target');
    }, 1000);
}

// Function called only when loading from storage
function addSavedTargetToListAndMap(targetData) {
    // Directly use the saved data, converting plain lat/lng object back to L.latLng
    if (targetData && targetData.latlng && typeof targetData.latlng.lat === 'number' && typeof targetData.latlng.lng === 'number') {
         const leafletLatLng = L.latLng(targetData.latlng.lat, targetData.latlng.lng);
         // Pass the full data including the L.latLng object to the main add function
         addTargetToListAndMap({...targetData, latlng: leafletLatLng });
    } else {
        console.warn("Skipping saved target with missing/invalid latlng data:", targetData);
    }
}


// Function called when the "Add Target" button is clicked
function addTarget() {
    const forceType = document.getElementById('force-type').value;
    const targetType = document.getElementById('target-type').value;
    const quantityInput = document.getElementById('target-quantity');
    const quantity = parseInt(quantityInput.value, 10);
    const locationInput = document.getElementById('target-location');
    const locationValue = locationInput.value.trim(); // Use trimmed value

    // --- Input Validation ---
    if (isNaN(quantity) || quantity <= 0) { // Check if quantity is a valid number > 0
         alert("Please enter a quantity greater than 0.");
         quantityInput.focus();
         return;
    }
     if (!locationValue) {
         alert("Please select a location on the map or grid by clicking.");
         return;
     }

    const locationParts = locationValue.split(' | ');
    let displayLocationText = locationValue; // Text to show in the list
    let processingLocationText = locationValue; // Text to parse coords from
    let gridInfo = "";
    let targetLatLng = null;
    let rawMgrsString = ""; // To store MGRS string for validation

    // --- Location Parsing Logic ---
    // Separate grid info from the rest for parsing coordinates
    if (locationParts.length > 1 && locationParts[0].startsWith('Grid:')) {
        gridInfo = ` (${locationParts[0]})`;
        // Use only the coordinate part for parsing lat/lon or MGRS
        processingLocationText = locationParts.slice(1).join(' | ');
        // Keep the full string for display unless MGRS is validated later
        displayLocationText = locationValue;
    } else {
        processingLocationText = locationValue; // Use the whole input if no grid info
        displayLocationText = locationValue;
    }

    // Try to extract Lat/Lon first from the processing text
    const latLngMatch = processingLocationText.match(/Lat: (-?\d+(\.\d+)?), Lon: (-?\d+(\.\d+)?)/); // Allow integer coords too
    if (latLngMatch) {
        targetLatLng = L.latLng(parseFloat(latLngMatch[1]), parseFloat(latLngMatch[3])); // Use correct capture groups
        // If Lat/Lon found, make it the primary display text besides grid info
        displayLocationText = `Lat: ${targetLatLng.lat.toFixed(6)}, Lon: ${targetLatLng.lng.toFixed(6)}`;
    }

    // Try to extract MGRS from the processing text
    if (processingLocationText.includes('MGRS:')) {
        rawMgrsString = processingLocationText.split('MGRS: ')[1].trim();
        if (!rawMgrsString) {
             // Handle empty MGRS string if it occurs
             console.warn("Empty MGRS string detected in input.");
             // Decide how to handle: maybe ignore MGRS part or require user input
        } else if (typeof mgrs !== 'undefined') {
            try {
                // *** MGRS Validation Step ***
                const convertedLatLng = mgrs.toLatLng(rawMgrsString); // Attempt conversion
                // If conversion succeeds and we didn't already get Lat/Lon, use it
                if (!targetLatLng) {
                    targetLatLng = L.latLng(convertedLatLng[0], convertedLatLng[1]);
                }
                 // Prioritize showing MGRS in the list if it's valid
                 displayLocationText = `MGRS: ${rawMgrsString}`;
            } catch (e) {
                console.error("MGRS conversion error:", e);
                // *** MGRS Validation Feedback ***
                alert(`Invalid MGRS format: "${rawMgrsString}". Please ensure it's correct or use Lat/Lon.`);
                return; // Stop processing if MGRS is explicitly provided but invalid
            }
        } else {
            // Should not happen if library is loaded, but good practice
            alert("MGRS library not loaded, cannot validate or use MGRS coordinates.");
            return;
        }
    }

    // If after all checks, we still don't have LatLng (e.g., bad input format)
    if (!targetLatLng) {
        alert("Could not determine target coordinates from input: " + locationValue + "\nPlease click the map to set a location.");
        return;
    }

    // --- Prepare Data and Add ---
    const targetData = {
        forceType,
        targetType,
        quantity,
        locationText: displayLocationText, // Use the cleaned display text
        gridInfo,
        latlng: targetLatLng // Pass the L.latLng object to the add helper
    };

    // Use the helper to add to list and map
    addTargetToListAndMap(targetData);

    // --- Post-Add Actions ---
    saveTargetsToLocalStorage(); // Save after adding a new target
    locationInput.value = ""; // Clear input field
    if (highlightedCell) {
        map.removeLayer(highlightedCell);
        highlightedCell = null;
    }
}

// Function to clear all targets
function clearAllTargets(confirmBeforeClear = true) { // Add optional flag
    let doClear = false;
    if (confirmBeforeClear) {
        doClear = confirm("Are you sure you want to remove ALL targets from the list, map, and saved data?");
    } else {
        doClear = true; // Skip confirmation if flag is false
    }

    if (doClear) {
        // Remove markers from map and clear array
        targetMarkers.forEach(marker => map.removeLayer(marker));
        targetMarkers = [];

        // Clear the list in the UI
        targetList.innerHTML = '';

        // Clear local storage only if confirmation was sought or implied
        if (confirmBeforeClear) {
             localStorage.removeItem(LOCAL_STORAGE_KEY);
             console.log("All targets cleared from UI, Map, and localStorage.");
        } else {
             console.log("All targets cleared from UI and Map (localStorage untouched).");
        }
    }
}

// Function to send data (with feedback using Fetch API)
async function sendTargetData() { // Changed to async function for await
    const targetListData = [];
    const targetListItems = targetList.querySelectorAll('li');

    targetListItems.forEach(item => {
        if (item.targetData) { // Use stored data from the list item
             targetListData.push(item.targetData); // targetData already has plain lat/lng
        }
    });

    if (targetListData.length === 0) {
        alert("Target list is empty. Add targets before sending.");
        return;
    }

    // --- Visual Feedback ---
    sendFeedbackEl.textContent = "Sending targets...";
    sendFeedbackEl.className = 'feedback-message'; // Reset classes
    const sendButton = document.getElementById('send-targets-button');
    sendButton.disabled = true; // Disable button while sending
    sendButton.classList.add('sending'); // Add class for sending style

    // --- Define the API endpoint ---
    // !!! This now points to your local backend server !!!
    const apiUrl = 'http://localhost:3000/api/sendTargets'; // Use full URL

    try {
        // --- Use Fetch API to send data ---
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Add any other headers like authorization tokens if needed
            },
            body: JSON.stringify(targetListData) // Convert target data array to JSON string
        });

        // Check if the server responded successfully (status code 2xx)
        if (response.ok) {
            // Optional: Process response data from server if needed
            // const responseData = await response.json();
            // console.log('Server response:', responseData);

            sendFeedbackEl.textContent = "Targets sent successfully!";
            sendFeedbackEl.classList.add('success');
            console.log('Target data sent successfully.');
            // Optionally clear targets after successful send?
            // clearAllTargets(false); // Example: Clear UI/Map but not storage

        } else {
            // Server responded with an error status code (4xx or 5xx)
            let errorText = `Server responded with status: ${response.status} ${response.statusText}`;
             try {
                 const serverError = await response.text(); // Try to get more details
                 if (serverError) {
                      errorText += `. Details: ${serverError.substring(0, 150)}`; // Limit length
                 }
             } catch (e) {
                  console.warn("Could not read error response body.");
             }
            sendFeedbackEl.textContent = errorText;
            sendFeedbackEl.classList.add('error');
            console.error(`Error sending target data: ${response.status} ${response.statusText}`);
        }

    } catch (error) {
        // Handle network errors or other issues with the fetch call itself
        sendFeedbackEl.textContent = "Network error. Could not send targets. Is the server running?";
        sendFeedbackEl.classList.add('error');
        console.error('Network error or other issue sending target data:', error);
    } finally {
        // Re-enable the button and clear feedback after a delay, regardless of success/failure
        sendButton.disabled = false; // Re-enable button
        sendButton.classList.remove('sending'); // Remove sending style
        setTimeout(() => {
            // Check if the feedback message hasn't changed (e.g., by another quick send attempt)
            if (sendFeedbackEl.textContent.includes("Targets sent successfully!") || sendFeedbackEl.textContent.includes("Error:") || sendFeedbackEl.textContent.includes("Network error.")) {
                 sendFeedbackEl.textContent = "";
                 sendFeedbackEl.className = 'feedback-message';
            }
        }, 5000); // Keep message for 5 seconds
    }
}


// --- Grid Drawing and Map Interaction ---

// Function to set up collapsible legend
function setupCollapsibleLegend() {
    const mapLegend = document.getElementById('map-legend');
    if (!mapLegend) return;

    const legendHeader = mapLegend.querySelector('h4');
    if (!legendHeader) return;

    // Set initial state (expanded by default)
    mapLegend.classList.add('expanded');

    // Toggle legend on header click
    legendHeader.addEventListener('click', () => {
        if (mapLegend.classList.contains('expanded')) {
            mapLegend.classList.remove('expanded');
            mapLegend.classList.add('collapsed');
        } else {
            mapLegend.classList.remove('collapsed');
            mapLegend.classList.add('expanded');
        }
    });
}

function clearGrid() {
    currentGridLines.forEach(line => map.removeLayer(line));
    currentGridLines = [];
    if (drawnRectangle) {
        map.removeLayer(drawnRectangle);
        drawnRectangle = null;
    }
    if (highlightedCell) {
        map.removeLayer(highlightedCell);
        highlightedCell = null;
    }
}

function drawGrid(bounds, gridSize) {
    clearGrid();
    const southWest = bounds.getSouthWest();
    const northEast = bounds.getNorthEast();
    // Use Leaflet's distanceTo for potentially better accuracy across latitudes
    const gridWidthMeters = southWest.distanceTo(L.latLng(southWest.lat, northEast.lng));
    const gridHeightMeters = southWest.distanceTo(L.latLng(northEast.lat, southWest.lng));
    // Ensure at least one row/column
    const numCols = Math.max(1, Math.ceil(gridWidthMeters / gridSize));
    const numRows = Math.max(1, Math.ceil(gridHeightMeters / gridSize));
    const latStep = (northEast.lat - southWest.lat) / numRows;
    const lngStep = (northEast.lng - southWest.lng) / numCols;
    const newGridLines = [];
    const gridLineOptions = { color: '#555', weight: 1, interactive: false }; // Darker grid lines
    // Draw Vertical Lines
    for (let i = 0; i <= numCols; i++) {
        const lng = southWest.lng + i * lngStep;
        newGridLines.push(L.polyline([L.latLng(southWest.lat, lng), L.latLng(northEast.lat, lng)], gridLineOptions).addTo(map));
    }
    // Draw Horizontal Lines
    for (let j = 0; j <= numRows; j++) {
        const lat = southWest.lat + j * latStep;
        newGridLines.push(L.polyline([L.latLng(lat, southWest.lng), L.latLng(lat, northEast.lng)], gridLineOptions).addTo(map));
    }
    currentGridLines = newGridLines;
    // Make drawn rectangle non-interactive with clearer styling
    drawnRectangle = L.rectangle(bounds, { color: '#3388ff', fillOpacity: 0.1, weight: 2, interactive: false }).addTo(map);
}

// Setup Leaflet.Draw control
// Enable the rectangle tool in the map toolbar to ensure it works.
const drawControl = new L.Control.Draw({
    draw: {
        polyline: false,
        polygon: false,
        circle: false,
        marker: false, // Disable standard marker draw tool
        circlemarker: false,
        rectangle: { // Enable the default rectangle tool button
             shapeOptions: { color: '#3388ff', fillOpacity: 0.1, weight: 2 },
             tooltip: { start: 'Click and drag to draw grid area.' }
        }
    },
    edit: {
        featureGroup: new L.FeatureGroup() // Required even if editing is disabled
    }
});
map.addControl(drawControl);

// Handle creation of the grid rectangle
map.on('draw:created', function (e) {
    if (e.layerType === 'rectangle') {
        const bounds = e.layer.getBounds();
        const gridSizeInput = document.getElementById('grid-size');
        if (!gridSizeInput) {
            console.error("Grid size input element not found.");
            return;
        }
        const gridSize = parseFloat(gridSizeInput.value);
        if (!isNaN(gridSize) && gridSize > 0) {
            drawGrid(bounds, gridSize);
        } else {
            alert("Please enter a valid positive grid size (e.g., 100).");
        }
        // We won't disable drawing automatically here if using the default button,
        // as the user might want to draw multiple rectangles.
    }
});

// Add event listener to the custom Draw Grid Area button
// Using DOMContentLoaded to ensure the button element exists
document.addEventListener('DOMContentLoaded', () => {
    // Set up the rectangle icon in the instructions to highlight the map tool
    const rectangleIcon = document.querySelector('.rectangle-icon');
    const rectangleButton = document.querySelector('.leaflet-draw-draw-rectangle');

    if (rectangleIcon && rectangleButton) {
        // Highlight the map tool when hovering over the icon in instructions
        rectangleIcon.addEventListener('mouseenter', () => {
            rectangleButton.classList.add('highlight-tool');
        });

        rectangleIcon.addEventListener('mouseleave', () => {
            rectangleButton.classList.remove('highlight-tool');
        });

        // Make the icon clickable to activate the draw tool
        rectangleIcon.style.cursor = 'pointer';
        rectangleIcon.addEventListener('click', () => {
            if (drawControl.handlers.rectangle) {
                drawControl.handlers.rectangle.enable();
                rectangleButton.classList.add('highlight-tool');

                // Remove highlight when drawing starts or is canceled
                const removeHighlight = () => {
                    rectangleButton.classList.remove('highlight-tool');
                };

                map.once('draw:drawstart', removeHighlight);
                map.once('draw:drawstop', removeHighlight);
                map.once('draw:canceled', removeHighlight);
            }
        });
    }

    // Add event listener for the Clear Grid Lines button
    const clearGridLinesButton = document.getElementById('clear-grid-lines-button');
    if (clearGridLinesButton) {
        clearGridLinesButton.addEventListener('click', clearGrid);
    } else {
         console.error("Clear Grid Lines Button element not found!");
    }

    // Set up collapsible legend
    setupCollapsibleLegend();

    // Load saved targets when the DOM is ready
    loadTargetsFromLocalStorage();
});


// Handle map clicks for location input and grid highlighting
map.on('click', (e) => {
    const clickedLatLng = e.latlng;
    const locationInput = document.getElementById('target-location');
    locationInput.value = ""; // Clear previous input first

    // Clear previous highlight
    if (highlightedCell) {
        map.removeLayer(highlightedCell);
        highlightedCell = null;
    }

    let locationString = "";
    let gridRef = "";
    let mgrsString = "";

    // --- Generate MGRS/LatLon String ---
    if (typeof mgrs !== 'undefined' && mgrs.toMGRS) {
        try {
            // Get MGRS with 5-digit precision (1m)
            mgrsString = mgrs.toMGRS(clickedLatLng, 5);
            locationString = `MGRS: ${mgrsString}`;
        } catch (err) {
            console.error("Error converting to MGRS on click:", err);
            // Fallback to Lat/Lon if MGRS fails
            locationString = `Lat: ${clickedLatLng.lat.toFixed(6)}, Lon: ${clickedLatLng.lng.toFixed(6)}`;
        }
    } else {
        // Fallback to Lat/Lon if mgrs library not loaded
        locationString = `Lat: ${clickedLatLng.lat.toFixed(6)}, Lon: ${clickedLatLng.lng.toFixed(6)}`;
    }

    // --- Check if inside drawn grid and highlight cell ---
    if (drawnRectangle && currentGridLines.length > 0 && drawnRectangle.getBounds().contains(clickedLatLng)) {
        const bounds = drawnRectangle.getBounds();
        const southWest = bounds.getSouthWest();
        const northEast = bounds.getNorthEast();
        const gridSizeInput = document.getElementById('grid-size'); // Check grid size input exists
        const gridSize = gridSizeInput ? parseFloat(gridSizeInput.value) : NaN;

        if (!isNaN(gridSize) && gridSize > 0) {
            // Recalculate grid dimensions for accuracy
            const gridWidthMeters = southWest.distanceTo(L.latLng(southWest.lat, northEast.lng));
            const gridHeightMeters = southWest.distanceTo(L.latLng(northEast.lat, southWest.lng));
            // Ensure at least one row/column
            const numCols = Math.max(1, Math.ceil(gridWidthMeters / gridSize));
            const numRows = Math.max(1, Math.ceil(gridHeightMeters / gridSize));
            const latStep = (northEast.lat - southWest.lat) / numRows;
            const lngStep = (northEast.lng - southWest.lng) / numCols;

            // Calculate which cell was clicked (ensure within bounds 0 to N-1)
            const row = Math.min(numRows - 1, Math.max(0, Math.floor((clickedLatLng.lat - southWest.lat) / latStep)));
            const col = Math.min(numCols - 1, Math.max(0, Math.floor((clickedLatLng.lng - southWest.lng) / lngStep)));

            // Calculate bounds of the clicked cell
            const cellSouthWest = L.latLng(southWest.lat + row * latStep, southWest.lng + col * lngStep);
            const cellNorthEast = L.latLng(southWest.lat + (row + 1) * latStep, southWest.lng + (col + 1) * lngStep);

            // Highlight the cell
            highlightedCell = L.rectangle([cellSouthWest, cellNorthEast], { color: 'lime', weight: 2, fillOpacity: 0.3, interactive: false }).addTo(map);

            // Create grid reference string (R1, C1 format)
            gridRef = `Grid: R${row + 1}, C${col + 1}`;
            // Prepend Grid Ref to the location string
            locationString = `${gridRef} | ${locationString}`;
        } else {
             console.warn("Grid present but grid size is invalid. Cannot calculate cell reference.");
             // Keep locationString as just MGRS/LatLon if grid size is bad
        }
    }

    // Update the input field with the final string
    locationInput.value = locationString;
});