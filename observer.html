<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fire Effect Observer</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="modern-theme.css">
    <link rel="stylesheet" href="retro-theme.css">
</head>
<body>
    <div class="container">
        <!-- Navigation Header -->
        <div class="screen-navigation">
            <h1>Fire Effect Observer</h1>
            <div class="nav-buttons">
                <button id="observer-nav-btn" class="nav-button active" disabled>Observer Screen</button>
                <button id="mortar-nav-btn" class="nav-button" onclick="window.location.href='mortar.html'">Mortar Screen</button>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-section">
                <div class="status-indicator" id="connection-status">
                    <span class="status-icon" id="connection-icon">●</span>
                    <span class="status-text" id="connection-text">Connecting...</span>
                </div>
            </div>
            <div class="status-section">
                <div class="status-indicator" id="target-count-status">
                    <span class="status-icon">🎯</span>
                    <span class="status-text" id="target-count-text">0 Targets</span>
                </div>
            </div>
            <div class="status-section">
                <div class="status-indicator" id="auto-save-status">
                    <span class="status-icon" id="save-icon">💾</span>
                    <span class="status-text" id="save-text">Auto-saved</span>
                </div>
            </div>
            <div class="status-section">
                <div class="status-indicator" id="grid-coordinates" title="Current mouse coordinates">
                    <span class="status-icon">📍</span>
                    <span class="status-text" id="coordinates-text">--°, --°</span>
                </div>
            </div>
        </div>

        <button type="button" class="collapsible">How to Use the Observer Screen</button>
        <div class="content">
            <ol>
                <li><strong>Pinpoint Target Location:</strong> Click on the map to mark the location of the target. The coordinates will automatically populate in the "Location" field.</li>
                <li><strong>Select Target Information:</strong>
                    <ul>
                        <li>Choose the "Force Type" (Friendly or Enemy) from the dropdown menu.</li>
                        <li>Select the "Type" of target (Infantry, Tank, MG Placement) from the dropdown menu.</li>
                        <li>Enter the "Quantity" of targets observed in the number input field.</li>
                    </ul>
                </li>
                <li><strong>Add Target to List:</strong> Click the "Add Target" button to add the target information to the "Target List" below.</li>
                <li><strong>Manage Target List:</strong>
                    <ul>
                        <li>Review the targets listed. Each target will show its Force Type, Type, Quantity, and Location.</li>
                        <li>To remove a target, click the "Remove" button next to the target in the list.</li>
                        <li>To clear all targets, click the "Clear All Targets" button.</li>
                    </ul>
                </li>
                <li><strong>Send Target Data:</strong> Once all identified targets have been accurately added to the list, click the "Send Targets" button to transmit the information. A feedback message will appear briefly to indicate the success or failure of the transmission.</li>
            </ol>
        </div>

        <h1>Fire Effect Observer</h1>
        <p class="intro">Use the map to pinpoint target locations. Select the force type, target type, and quantity, then add the target to the list. Once all targets are marked, send the data.</p>

        <div class="map-controls-wrapper">
            <div id="map-container">
                <div id="map"></div>
                <div id="map-legend" class="collapsed">
                    <h4>Target Type Legend</h4>
                    <div class="legend-content">
                    <div class="legend-items">
                        <div class="legend-item">
                            <div class="legend-tank"></div>
                            <span>Tank</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-infantry"></div>
                            <span>Infantry</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-mg"></div>
                            <span>MG Placement</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-artillery"></div>
                            <span>Artillery</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-antiair">
                                <div class="cross-horizontal"></div>
                                <div class="cross-vertical"></div>
                            </div>
                            <span>Anti-Aircraft</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-recon">
                                <svg width="20" height="20" viewBox="0 0 20 20">
                                    <polygon points="5,0 15,0 20,10 15,20 5,20 0,10" fill="#777" stroke="#777" stroke-width="1" />
                                </svg>
                            </div>
                            <span>Reconnaissance</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-apc"></div>
                            <span>APC</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-heli">
                                <svg width="20" height="20" viewBox="0 0 20 20">
                                    <polygon points="10,0 20,7 16,20 4,20 0,7" fill="#777" stroke="#777" stroke-width="1" />
                                </svg>
                            </div>
                            <span>Helicopter</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-command">
                                <svg width="20" height="20" viewBox="0 0 20 20">
                                    <polygon points="10,0 13,7 20,7 15,11 17,18 10,14 3,18 5,11 0,7 7,7" fill="#777" stroke="#777" stroke-width="1" />
                                </svg>
                            </div>
                            <span>Command Post</span>
                        </div>
                    </div>
                    <div class="force-type-indicators">
                        <div class="legend-item">
                            <div class="legend-icon legend-friendly"></div>
                            <span>Friendly</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-icon legend-enemy"></div>
                            <span>Enemy</span>
                        </div>
                    </div>
                    </div>
                </div>
            </div>

            <div id="controls-container">
            <div class="control-panel">

                 <section id="grid-control">
                    <h3>Grid Control</h3>
                    <div class="grid-instructions">
                        <p>To create a grid, click the rectangle tool <span class="tool-icon rectangle-icon"></span> on the map, then draw a rectangle area.</p>
                    </div>
                    <div class="form-group">
                        <label for="grid-size">Grid Size (meters):</label>
                        <input type="number" id="grid-size" value="100">
                    </div>
                     <button id="clear-grid-lines-button">Clear Grid Lines</button>
                     <button id="measure-distance-button" onclick="toggleMeasurementMode()" title="Measure distance between two points on the map (Ctrl+M)">📏 Measure Distance</button>

                     <div class="zoom-controls">
                         <label>Quick Zoom:</label>
                         <div class="zoom-buttons">
                             <button onclick="setZoomLevel(10)" title="Strategic view">🌍 Strategic</button>
                             <button onclick="setZoomLevel(13)" title="Operational view">🗺️ Operational</button>
                             <button onclick="setZoomLevel(16)" title="Tactical view">🎯 Tactical</button>
                         </div>
                         <div class="zoom-indicator">
                             <span>Zoom: </span><span id="current-zoom">13</span>
                         </div>
                     </div>
                 </section>

                <section id="target-information">
                    <h2>Target Information</h2>
                    <div class="form-group">
                        <label for="force-type">Force Type:</label>
                        <select id="force-type" title="Classify the observed unit as friendly or enemy forces">
                            <option value="friendly">Friendly</option>
                            <option value="enemy">Enemy</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="target-type">Type:</label>
                        <select id="target-type" title="Select the type of military unit or equipment observed">
                            <option value="infantry">Infantry</option>
                            <option value="tank">Tank</option>
                            <option value="mg">MG Placement</option>
                            <option value="artillery">Artillery</option>
                            <option value="antiair">Anti-Aircraft</option>
                            <option value="recon">Reconnaissance</option>
                            <option value="apc">Armored Personnel Carrier</option>
                            <option value="heli">Helicopter</option>
                            <option value="command">Command Post</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="target-quantity">Quantity:</label>
                        <input type="number" id="target-quantity" value="1" min="1" title="Number of units or targets observed at this location">
                    </div>
                    <div class="form-group">
                        <label for="target-location">Location (Click Map):</label>
                        <input type="text" id="target-location" readonly placeholder="Latitude, Longitude" title="Geographic coordinates of the target - click on the map to set">
                    </div>
                    <div class="button-group">
                        <button id="add-target-button" onclick="addTarget()">Add Target</button>
                        <button id="send-targets-button" onclick="confirmSendTargets()">Send Targets</button>
                    </div>
                </section>
            </div>

             <div class="control-panel">
                <!-- Target Summary Dashboard -->
                <section id="target-summary-section">
                    <h2>Target Summary</h2>
                    <div class="summary-dashboard">
                        <div class="summary-card">
                            <div class="summary-icon">🎯</div>
                            <div class="summary-content">
                                <div class="summary-number" id="total-targets">0</div>
                                <div class="summary-label">Total Targets</div>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-icon enemy">🔴</div>
                            <div class="summary-content">
                                <div class="summary-number" id="enemy-targets">0</div>
                                <div class="summary-label">Enemy</div>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-icon friendly">🔵</div>
                            <div class="summary-content">
                                <div class="summary-number" id="friendly-targets">0</div>
                                <div class="summary-label">Friendly</div>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-icon priority">⚡</div>
                            <div class="summary-content">
                                <div class="summary-number" id="priority-targets">0</div>
                                <div class="summary-label">High Priority</div>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="target-list-section">
                    <h2>Target List</h2>
                    <ul id="target-list-items">
                        </ul>
                    <button id="clear-targets-button" onclick="confirmClearTargets()">Clear All Targets</button>
                </section>
            </div>
        </div>
        </div>

         <div class="send-feedback-area">
             <div id="send-feedback" class="feedback-message"></div>
         </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mgrs-js@1.0.1/mgrs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    <script src="script.js"></script>
    <script src="theme-switcher.js"></script>
    <script>
        var coll = document.querySelectorAll(".collapsible");
        var i;

        for (i = 0; i < coll.length; i++) {
            coll[i].addEventListener("click", function() {
                this.classList.toggle("active");
                var content = this.nextElementSibling;
                if (content.style.maxHeight){
                    // Collapsing
                    content.style.maxHeight = null;
                    content.style.visibility = "hidden";
                    content.style.opacity = "0";
                    content.style.padding = "0";
                } else {
                    // Expanding
                    content.style.visibility = "visible";
                    content.style.opacity = "1";
                    content.style.padding = "calc(var(--spacing-unit) * 3.5)";
                    content.style.maxHeight = content.scrollHeight + "px";
                }
            });
        }
    </script>
</body>
</html>